package leads

import (
	"fmt"
	"log"
	"testing"

	"github.com/gin-gonic/gin"
)

// TestCSVFeatures 测试从 CSV 读取特征数据的功能
func TestCSVFeatures(t *testing.T) {
	// 创建测试用的 gin context
	gin.SetMode(gin.TestMode)
	ctx, _ := gin.CreateTestContext(nil)

	// 设置测试用的 zlog context
	ctx.Set("trace_id", "test_trace_id")

	// 测试数据
	testCases := []struct {
		courseId   int64
		studentUid int64
		desc       string
	}{
		{12345, 67890, "测试学生1 - 高出勤率"},
		{12345, 67891, "测试学生2 - 中等表现"},
		{12345, 67892, "测试学生3 - 优秀表现"},
		{12345, 67893, "测试学生4 - 较低表现"},
		{12345, 67894, "测试学生5 - 良好表现"},
		{12345, 99999, "不存在的学生 - 测试错误处理"},
	}

	fmt.Println("=== CSV 特征数据测试开始 ===")
	fmt.Println()

	for i, testCase := range testCases {
		fmt.Printf("测试用例 %d: %s\n", i+1, testCase.desc)
		fmt.Printf("课程ID: %d, 学生ID: %d\n", testCase.courseId, testCase.studentUid)
		fmt.Println("----------------------------------------")

		// 调用 buildFeatures 函数
		features, err := buildFeatures(ctx, testCase.courseId, testCase.studentUid)
		if err != nil {
			fmt.Printf("❌ 错误: %v\n", err)
		} else {
			fmt.Printf("✅ 成功获取 %d 个特征\n", len(features))

			// 显示特征详情
			if len(features) > 0 {
				fmt.Println("\n特征详情:")
				for _, feature := range features {
					fmt.Printf("  %d. %s (%s): %s\n",
						feature.Index, feature.Desc, feature.Name, feature.Value)
				}
			} else {
				fmt.Println("  无特征数据")
			}
		}

		fmt.Println()
		fmt.Println("========================================")
		fmt.Println()
	}

	fmt.Println("=== CSV 特征数据测试结束 ===")
}

// TestCSVDataReading 测试 CSV 数据读取功能
func TestCSVDataReading(t *testing.T) {
	gin.SetMode(gin.TestMode)
	ctx, _ := gin.CreateTestContext(nil)
	ctx.Set("trace_id", "test_csv_reading")

	fmt.Println("=== CSV 数据读取测试开始 ===")
	fmt.Println()

	// 测试读取 CSV 数据
	courseId := int64(12345)
	studentUid := int64(67890)

	dataMap, err := readFeaturesFromCSV(ctx, courseId, studentUid)
	if err != nil {
		fmt.Printf("❌ CSV 读取失败: %v\n", err)
		return
	}

	fmt.Printf("✅ 成功读取 CSV 数据，字段数量: %d\n", len(dataMap))
	fmt.Println("\n原始数据内容:")
	for key, value := range dataMap {
		fmt.Printf("  %s: %v (类型: %T)\n", key, value, value)
	}

	fmt.Println()
	fmt.Println("=== CSV 数据读取测试结束 ===")
}

// TestFeatureFormatting 测试特征格式化功能
func TestFeatureFormatting(t *testing.T) {
	gin.SetMode(gin.TestMode)
	ctx, _ := gin.CreateTestContext(nil)
	ctx.Set("trace_id", "test_formatting")

	fmt.Println("=== 特征格式化测试开始 ===")
	fmt.Println()

	// 创建测试配置
	testConfigs := []LeadsFeatureConfig{
		{
			Name:         "attend_rate",
			Desc:         "出勤率",
			Type:         "percentage",
			DecimalPlace: 1,
		},
		{
			Name:       "last_login_time",
			Desc:       "最后登录时间",
			Type:       "timestamp",
			TimeFormat: "2006-01-02 15:04:05",
		},
		{
			Name: "learning_level",
			Desc: "学习等级",
			Type: "enum",
			Mapping: map[string]string{
				"0": "初级",
				"1": "中级",
				"2": "高级",
				"3": "专家",
			},
		},
		{
			Name:           "total_payment",
			Desc:           "总支付金额",
			Type:           "currency",
			DecimalPlace:   2,
			DivisionFactor: 100,
		},
	}

	// 测试数据
	testValues := map[string]interface{}{
		"attend_rate":     0.85,
		"last_login_time": int64(1703980800),
		"learning_level":  2,
		"total_payment":   29800,
	}

	for _, config := range testConfigs {
		if value, exists := testValues[config.Name]; exists {
			formatted := FormatFeatureValue(ctx, config, value)
			fmt.Printf("字段: %s (%s)\n", config.Name, config.Desc)
			fmt.Printf("  原始值: %v (类型: %T)\n", value, value)
			fmt.Printf("  格式化类型: %s\n", config.Type)
			fmt.Printf("  格式化结果: %s\n", formatted)
			fmt.Println()
		}
	}

	fmt.Println("=== 特征格式化测试结束 ===")
}

// RunAllTests 运行所有测试
func RunAllTests(t *testing.T) {
	// 初始化日志
	log.SetFlags(log.LstdFlags | log.Lshortfile)

	fmt.Println("开始运行 CSV 特征数据测试...")
	fmt.Println()

	// 运行各项测试
	TestCSVDataReading(t)
	fmt.Println()

	TestFeatureFormatting(t)
	fmt.Println()

	TestCSVFeatures(t)

	fmt.Println("所有测试完成！")
}
