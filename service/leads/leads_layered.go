package leads

import (
	"assistantdeskgo/api/dataproxy"
	"assistantdeskgo/dto/dtoleads"
	"encoding/csv"
	"fmt"
	"os"
	"sort"
	"strconv"
	"time"

	"git.zuoyebang.cc/pkg/golib/v2/zlog"
	"github.com/gin-gonic/gin"
	"github.com/spf13/cast"
)

// 常量
const (
	dateFormatYYYYMMDD    = "20060102"
	dateFormatMMDD        = "01/02"
	dateFormatChineseMMDD = "01月02日"
)

// GetLeadsLayerDetailInfo retrieves layered detail information for a specific course and student.
func GetLeadsLayerDetailInfo(ctx *gin.Context, req dtoleads.GetLeadsLayerDetailInfoReq) (rsp dtoleads.GetLeadsLayerDetailInfoRsp, err error) {
	// rsp.PurchaseIntentions = make([]dtoleads.PurchaseIntention, 0)
	// rsp.Milestones = make([]dtoleads.Milestone, 0)
	// rsp.Features = make([]dtoleads.Feature, 0)

	// result, err := dataproxy.GetLeadsAdsBaseModelByCourse(ctx, dataproxy.GetLeadsAdsBaseModelReq{
	// 	CourseId:    req.CourseId,
	// 	StudentUids: cast.ToString(req.StudentUid),
	// 	Fields:      strings.Join([]string{"wx_add_time", "mdc_time", "xzk_time", "inclass_time", "trans_score", "trans_level", "date"}, ","),
	// })
	// if err != nil || len(result.List) == 0 {
	// 	return
	// }

	// models := result.List
	// sort.Slice(models, func(i, j int) bool {
	// 	return models[i].Date > models[j].Date
	// })
	// latestModel := models[0]

	// rsp.Milestones, _ = buildMilestones(latestModel)
	// rsp.PurchaseIntentions, _ = buildPurchaseIntentions(getModelsByAllocTime(models, req.AllocTime))
	// rsp.RefreshTime = formatDate(latestModel.Date, dateFormatChineseMMDD)
	// rsp.TransLevel = getTransLevelDesc(latestModel.TransLevel)
	rsp.Features, _ = buildFeatures(ctx, req.CourseId, req.StudentUid)

	return rsp, nil
}

// readFeaturesFromCSV 从 CSV 文件读取特征数据
func readFeaturesFromCSV(ctx *gin.Context, courseId, studentUid int64) (map[string]any, error) {
	// CSV 文件路径
	csvFile := "数据示例.csv"

	// 打开 CSV 文件
	file, err := os.Open(csvFile)
	if err != nil {
		zlog.Errorf(ctx, "Failed to open CSV file %s: %+v", csvFile, err)
		return nil, fmt.Errorf("failed to open CSV file: %w", err)
	}
	defer file.Close()

	// 创建 CSV 读取器
	reader := csv.NewReader(file)

	// 读取所有记录
	records, err := reader.ReadAll()
	if err != nil {
		zlog.Errorf(ctx, "Failed to read CSV file %s: %+v", csvFile, err)
		return nil, fmt.Errorf("failed to read CSV file: %w", err)
	}

	if len(records) < 2 {
		zlog.Errorf(ctx, "CSV file %s has insufficient data", csvFile)
		return nil, fmt.Errorf("CSV file has insufficient data")
	}

	// 第一行是标题
	headers := records[0]

	// 查找匹配的学生数据
	for i := 1; i < len(records); i++ {
		record := records[i]
		if len(record) != len(headers) {
			zlog.Warnf(ctx, "CSV row %d has mismatched column count", i)
			continue
		}

		// 检查 course_id 和 student_uid 是否匹配
		var recordCourseId, recordStudentUid int64
		for j, header := range headers {
			if header == "course_id" {
				recordCourseId, _ = strconv.ParseInt(record[j], 10, 64)
			} else if header == "student_uid" {
				recordStudentUid, _ = strconv.ParseInt(record[j], 10, 64)
			}
		}

		if recordCourseId == courseId && recordStudentUid == studentUid {
			// 找到匹配的记录，构建数据映射
			dataMap := make(map[string]any)
			for j, header := range headers {
				if j < len(record) {
					// 尝试转换为数值类型
					if intVal, err := strconv.ParseInt(record[j], 10, 64); err == nil {
						dataMap[header] = intVal
					} else if floatVal, err := strconv.ParseFloat(record[j], 64); err == nil {
						dataMap[header] = floatVal
					} else {
						dataMap[header] = record[j]
					}
				}
			}
			zlog.Infof(ctx, "Found CSV data for courseId: %d, studentUid: %d", courseId, studentUid)
			return dataMap, nil
		}
	}

	zlog.Warnf(ctx, "No matching data found in CSV for courseId: %d, studentUid: %d", courseId, studentUid)
	return make(map[string]any), nil
}

// buildFeatures constructs a sorted list of features from the model
// 根据 LeadsFeatureConfig 配置构建特征列表，严格按照配置的顺序和内容展示
// 修改为从 CSV 文件读取测试数据
func buildFeatures(ctx *gin.Context, courseId, studentUid int64) ([]dtoleads.Feature, error) {
	// 获取需要展示的特征配置
	LeadsFeatureConfig, err := GetLeadsFeatureConfig(ctx)
	if err != nil || len(LeadsFeatureConfig) == 0 {
		zlog.Warnf(ctx, "LeadsFeatureConfig found no config")
		return make([]dtoleads.Feature, 0), nil
	}

	// 从 CSV 文件读取学生特征数据
	dataMap, err := readFeaturesFromCSV(ctx, courseId, studentUid)
	if err != nil {
		zlog.Errorf(ctx, "readFeaturesFromCSV failed, courseId: %d, studentUid: %d, err: %+v",
			courseId, studentUid, err)
		return nil, err
	}

	zlog.Infof(ctx, "CSV data loaded for courseId: %d, studentUid: %d, fields count: %d",
		courseId, studentUid, len(dataMap))

	// 按配置顺序构建特征列表，确保返回的特征列表长度与配置字段数量一致
	features := make([]dtoleads.Feature, 0, len(LeadsFeatureConfig))
	for i, config := range LeadsFeatureConfig {
		var valueStr string

		// 检查配置的字段名是否在数据中存在
		if value, exists := dataMap[config.Name]; exists && value != nil {
			// 使用配置的格式化规则处理字段值
			valueStr = FormatFeatureValue(ctx, config, value)
		} else {
			// 处理缺失字段：在对应位置插入空值
			valueStr = ""
			zlog.Warnf(ctx, "Field %s is missing or nil in CSV data, using empty value", config.Name)
		}

		// 无论字段是否存在都要添加到结果中，保持配置的顺序
		feature := dtoleads.Feature{
			Name:  config.Name,
			Desc:  config.Desc,
			Value: valueStr,
			Index: i + 1,
		}
		features = append(features, feature)
	}

	return features, nil
}

// MilestoneConfig defines the mapping for milestone fields.
type MilestoneConfig struct {
	Timestamp int64
	Desc      string
}

// buildMilestones constructs a list of milestones based on model timestamps.
func buildMilestones(model dataproxy.GetLeadsAdsBaseModel) ([]dtoleads.Milestone, error) {
	configs := []MilestoneConfig{
		{model.WxAddTime, "加微时间"},
		{model.MdcTime, "摸底测时间"},
		{model.XzkTime, "1V1出镜时间"},
		{model.InclassTime, "到课时间"},
	}

	milestones := make([]dtoleads.Milestone, 0, len(configs))
	for _, cfg := range configs {
		if cfg.Timestamp > 0 {
			milestones = append(milestones, dtoleads.Milestone{
				Date: formatTimestampToString(cfg.Timestamp, dateFormatMMDD),
				Desc: cfg.Desc,
			})
		}
	}

	return milestones, nil
}

// buildPurchaseIntentions constructs a list of purchase intentions sorted by date.
func buildPurchaseIntentions(models []dataproxy.GetLeadsAdsBaseModel) ([]dtoleads.PurchaseIntention, error) {
	if len(models) == 0 {
		return make([]dtoleads.PurchaseIntention, 0), nil
	}

	// Pre-allocate with estimated capacity
	intentions := make([]dtoleads.PurchaseIntention, 0, len(models))

	// Sort models by date in ascending order
	sortedModels := make([]dataproxy.GetLeadsAdsBaseModel, len(models))
	copy(sortedModels, models)
	sort.Slice(sortedModels, func(i, j int) bool {
		return sortedModels[i].Date < sortedModels[j].Date
	})

	for _, model := range sortedModels {
		intentions = append(intentions, dtoleads.PurchaseIntention{
			Date:  formatDate(model.Date, dateFormatMMDD),
			Score: model.TransScore,
		})
	}

	return intentions, nil
}

// getModelsByAllocTime filters models within the date range around allocTime.
func getModelsByAllocTime(models []dataproxy.GetLeadsAdsBaseModel, allocTime int64) []dataproxy.GetLeadsAdsBaseModel {
	if allocTime <= 0 {
		return nil
	}

	// 这里的时间范围是例子分配时间的当天和后14天
	startDate := time.Unix(allocTime, 0)
	endDate := time.Unix(allocTime, 0).AddDate(0, 0, 14)
	startDateInt := formatTimeToInt(startDate, dateFormatYYYYMMDD)
	endDateInt := formatTimeToInt(endDate, dateFormatYYYYMMDD)

	// Pre-allocate with estimated capacity
	filtered := make([]dataproxy.GetLeadsAdsBaseModel, 0, len(models))
	for _, model := range models {
		if model.Date >= startDateInt && model.Date <= endDateInt {
			filtered = append(filtered, model)
		}
	}

	return filtered
}

// formatTimestampToString formats a Unix timestamp to the specified format.
func formatTimestampToString(timestamp int64, format string) string {
	if timestamp <= 0 {
		return ""
	}
	return time.Unix(timestamp, 0).Format(format)
}

// formatTimeToInt formats a time.Time to an integer date (e.g., 20230101).
func formatTimeToInt(t time.Time, format string) int64 {
	dateStr := t.Format(format)
	return cast.ToInt64(dateStr)
}

func getTransLevelDesc(transLevel int) string {
	switch transLevel {
	case 0:
		return "新"
	case 1:
		return "高"
	case 2:
		return "中"
	case 3:
		return "低"
	default:
		return "新"
	}
}

func formatDate(dateInt int64, format string) string {
	dateStr := cast.ToString(dateInt)
	if len(dateStr) != 8 {
		return ""
	}

	t, err := time.Parse(dateFormatYYYYMMDD, dateStr)
	if err != nil {
		return ""
	}
	return t.Format(format)
}
